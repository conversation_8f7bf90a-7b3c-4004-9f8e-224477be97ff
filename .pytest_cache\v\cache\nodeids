["tests/test_api_v3_integration.py::TestAPIV3Integration::test_get_tools_endpoint", "tests/test_api_v3_integration.py::TestAPIV3Integration::test_health_check_endpoint", "tests/test_icp_planning.py::TestICPPlanning::test_action_selection", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_missing_context", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_missing_tools", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_success", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_tool_failure_handling", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_with_multiple_iterations", "tests/test_icp_planning.py::TestICPPlanning::test_icp_tools_registration", "tests/test_icp_planning.py::TestICPPlanning::test_planning_thought_generation", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_complete_intent_analysis_flow", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_context_preparation_missing_analysis", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_framework_analysis_missing_tools", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_framework_analysis_success", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_preference_analysis_missing_framework", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_preference_analysis_success", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_prepare_planning_context_success", "tests/test_performance.py::TestPerformance::test_concurrent_requests", "tests/test_performance.py::TestPerformance::test_event_bus_performance", "tests/test_performance.py::TestPerformance::test_multiple_requests_stability", "tests/test_performance.py::TestPerformance::test_response_time_single_request", "tests/test_performance.py::TestPerformance::test_tool_registry_performance", "tests/test_unified_architecture.py::TestStandardAgentState::test_state_structure", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_initialize_task", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_error", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_phase_end", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_phase_start", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_sync_from_agent_state", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_execute_action_tool", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_execute_action_tool_with_event_bus", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_get_tool_info", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_register_action_tool", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_register_planner_tool", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_complete_workflow_success", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_error_handling_in_workflow", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_event_bus_integration", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_state_synchronization", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_streaming_workflow", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_tool_registry_integration", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_workflow_with_real_data_simulation"]