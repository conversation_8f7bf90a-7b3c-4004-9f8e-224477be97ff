"""
LangGraph 节点函数实现 (V3.0 - 统一架构版)

每个节点函数负责执行特定的任务步骤，并更新状态。
支持两阶段工作流：意图分析 + ICP迭代规划
"""
import asyncio
import json
import logging
from typing import Dict, Any
from datetime import datetime
from .state import StandardAgentState, TravelPlanState
from src.tools.unified_registry import unified_registry
from src.core.logger import get_logger

logger = get_logger("travel_planner_nodes")


def analyze_core_intent(state: TravelPlanState) -> Dict[str, Any]:
    """分析核心意图节点
    
    解析用户的原始查询，提取目的地、时间、偏好等核心信息。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析核心意图")
    
    # TODO: 调用分析服务，获得结构化数据
    # core_intent = analysis_service.analyze_core_intent(state['original_query'])
    
    # 临时实现 - 简单解析
    query = state.get('original_query', '')
    destinations = []
    
    # 简单的目的地提取逻辑
    if '上海' in query:
        destinations.append('上海')
    if '北京' in query:
        destinations.append('北京')
    if '杭州' in query:
        destinations.append('杭州')
    
    if not destinations:
        destinations = ['上海']  # 默认目的地
    
    core_intent = {
        'destinations': destinations,
        'duration_days': 3,  # 默认3天
        'travel_type': 'leisure',
        'budget_range': 'medium'
    }
    
    # 生成旁白文本
    narration_text = f"我理解您想要规划一个{len(destinations)}个城市的旅行，主要目的地包括：{', '.join(destinations)}"
    
    return {
        "destinations": destinations,
        "core_intent": core_intent,
        "current_narration_text": narration_text,
        "current_step": "核心意图分析完成",
        "progress_percentage": 20
    }


def analyze_multi_city_strategy(state: TravelPlanState) -> Dict[str, Any]:
    """分析多城市策略节点
    
    如果识别出多个目的地，则生成宏观策略。
    在交互模式下，设置clarification_needed标志以暂停并等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析多城市策略")
    
    destinations = state.get("destinations", [])
    
    if len(destinations) > 1:
        # 生成多城市策略
        total_days = state.get("core_intent", {}).get("duration_days", 3)
        days_per_city = max(1, total_days // len(destinations))
        
        strategy = {
            "order": destinations,
            "split": [
                {"city": city, "days": days_per_city} 
                for city in destinations
            ]
        }
        
        narration_text = f"建议您按照 {' -> '.join(destinations)} 的顺序游览，每个城市安排{days_per_city}天"
        
        # 根据模式决定是否需要澄清
        needs_clarification = state.get("execution_mode") == "interactive"
        
        return {
            "multi_city_strategy": strategy,
            "current_narration_text": narration_text,
            "clarification_needed": "multi_city_strategy" if needs_clarification else None,
            "current_step": "多城市策略分析完成",
            "progress_percentage": 30
        }
    
    # 单目的地，直接跳过
    return {
        "current_step": "单目的地，跳过多城市策略",
        "progress_percentage": 30
    }


def analyze_driving_context(state: TravelPlanState) -> Dict[str, Any]:
    """分析驾驶情境节点
    
    分析用户的车辆信息和驾驶需求。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析驾驶情境")
    
    # TODO: 调用用户画像服务获取车辆信息
    # vehicle_info = user_profile_service.get_vehicle_info(state['user_id'])
    
    # 临时实现
    vehicle_info = {
        "model": "Tesla Model Y",
        "nominal_range_km": 450,
        "charge_type": "fast"
    }
    
    # 设定驾驶策略
    if vehicle_info and vehicle_info.get("nominal_range_km"):
        driving_strategy = "range_aware"
        planning_range_km = vehicle_info["nominal_range_km"] * 0.8  # 保守系数
        narration_text = f"检测到您的{vehicle_info['model']}，我会按照续航的80%为您规划路线，确保行程安全"
    else:
        driving_strategy = "general_assistance"
        planning_range_km = None
        narration_text = "我会为您的自驾行程提供停车场和充电站信息"
    
    return {
        "user_vehicle_info": vehicle_info,
        "driving_strategy": driving_strategy,
        "planning_range_km": planning_range_km,
        "range_buffer_factor": 0.8,
        "current_narration_text": narration_text,
        "current_step": "驾驶情境分析完成",
        "progress_percentage": 40
    }


def analyze_preferences(state: TravelPlanState) -> Dict[str, Any]:
    """分析用户偏好节点
    
    整合分析景点、美食、住宿等偏好。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析用户偏好")
    
    # TODO: 调用分析服务
    # attraction_prefs = analysis_service.analyze_attraction_preferences(...)
    # food_prefs = analysis_service.analyze_food_preferences(...)
    # accommodation_prefs = analysis_service.analyze_accommodation_preferences(...)
    
    # 临时实现
    attraction_prefs = {
        "types": ["文化古迹", "自然风光"],
        "crowd_preference": "适中",
        "activity_level": "轻松"
    }
    
    food_prefs = {
        "cuisine_types": ["本地特色", "川菜"],
        "price_range": "中等",
        "dietary_restrictions": []
    }
    
    accommodation_prefs = {
        "type": "酒店",
        "star_rating": "4星",
        "location_preference": "市中心"
    }
    
    narration_text = "根据您的偏好，我会为您推荐文化古迹和自然风光，安排本地特色美食，选择市中心的4星酒店"
    
    return {
        "attraction_preferences": attraction_prefs,
        "food_preferences": food_prefs,
        "accommodation_preferences": accommodation_prefs,
        "current_narration_text": narration_text,
        "current_step": "用户偏好分析完成",
        "progress_percentage": 50
    }


def wait_for_user_input(state: TravelPlanState) -> Dict[str, Any]:
    """等待用户输入节点
    
    在交互模式下暂停执行，等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 等待用户输入")
    
    # 这个节点主要是标记状态，实际的等待逻辑在图的执行层处理
    return {
        "current_step": "等待用户确认",
        "progress_percentage": state.get("progress_percentage", 50)
    }


def route_after_analysis(state: TravelPlanState) -> str:
    """分析后的路由函数
    
    决定下一步走向：是否需要等待用户输入。
    """
    if state.get("clarification_needed"):
        return "wait_for_user_input"
    else:
        return "continue_analysis"


def decide_planning_or_end(state: TravelPlanState) -> str:
    """决定是继续规划还是结束

    在分析的最后阶段决定是继续规划还是结束。
    """
    # 在自动模式下，总是继续
    if state.get("execution_mode") == "automatic":
        return "execute_planning_stage"

    # 在交互模式下，根据用户反馈决定
    if state.get("user_feedback") == "proceed":
        return "execute_planning_stage"
    else:
        return "__end__"


# ==================== V3.0 新增节点：两阶段意图分析 ====================

async def run_framework_analysis(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行核心框架分析 (V3.0)

    分析用户的旅行核心需求，包括目的地、天数、主题、多城市策略和自驾情境
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始核心框架分析")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "framework_analysis",
            "核心框架分析",
            "正在分析您的旅行核心需求和框架..."
        )

    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_framework_analysis_prompt")
        if not format_prompt:
            raise ValueError("format_framework_analysis_prompt tool not found")

        # 准备输入数据
        user_query = ""
        messages = state.get("messages", [])
        if messages:
            # 处理LangGraph消息格式
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                user_query = last_message.content
            elif isinstance(last_message, dict):
                user_query = last_message.get("content", "")
            else:
                user_query = str(last_message)

        user_profile = state.get("user_profile", {})
        current_time = datetime.now().isoformat()

        # 格式化提示词
        prompt = format_prompt(user_query, user_profile, current_time)

        # TODO: 调用LLM进行分析
        # 这里需要集成实际的LLM服务
        # response = await reasoning_service.call_llm(prompt)

        # 临时模拟分析结果
        framework_analysis = {
            "core_intent": {
                "destinations": ["北京"],
                "travel_days": 3,
                "travel_theme": ["文化", "休闲"],
                "budget_range": "中等",
                "group_size": 2,
                "group_members": ["成人"],
                "departure_city": "上海",
                "travel_time": "近期",
                "special_requirements": []
            },
            "multi_city_strategy": {
                "is_multi_city": False,
                "city_priority": ["北京"],
                "transportation_between_cities": "高铁",
                "time_allocation": {"北京": 3},
                "route_optimization": "单城市深度游"
            },
            "driving_context": {
                "has_driving_needs": False,
                "driving_scenarios": [],
                "vehicle_requirements": "",
                "parking_considerations": [],
                "driving_experience_level": "一般",
                "fuel_type_preference": "汽油"
            },
            "analysis_confidence": {
                "core_intent_confidence": 0.9,
                "multi_city_confidence": 0.8,
                "driving_context_confidence": 0.7,
                "overall_confidence": 0.8
            },
            "clarification_needed": [],
            "assumptions_made": ["假设用户偏好文化类景点"]
        }

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "framework_analysis",
                "success",
                framework_analysis
            )

        logger.info(f"[{task_id}] 核心框架分析完成")

        return {
            "framework_analysis": framework_analysis,
            "current_phase": "framework_analysis_completed"
        }

    except Exception as e:
        logger.error(f"[{task_id}] 核心框架分析失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "framework_analysis")

        return {
            "has_error": True,
            "error_message": f"核心框架分析失败: {str(e)}",
            "current_phase": "error"
        }


async def run_preference_analysis(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行个性化偏好分析 (V3.0)

    基于核心框架分析结果，深入分析用户的景点、美食、住宿偏好
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始个性化偏好分析")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "preference_analysis",
            "个性化偏好分析",
            "正在深入分析您的景点、美食、住宿偏好..."
        )

    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_preference_analysis_prompt")
        if not format_prompt:
            raise ValueError("format_preference_analysis_prompt tool not found")

        # 获取前一步的结果
        framework_result = state.get("framework_analysis", {})
        if not framework_result:
            raise ValueError("framework_analysis result not found")

        # 准备输入数据
        user_query = ""
        messages = state.get("messages", [])
        if messages:
            # 处理LangGraph消息格式
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                user_query = last_message.content
            elif isinstance(last_message, dict):
                user_query = last_message.get("content", "")
            else:
                user_query = str(last_message)

        user_profile = state.get("user_profile", {})
        current_time = datetime.now().isoformat()

        # 格式化提示词
        prompt = format_prompt(user_query, user_profile, framework_result, current_time)

        # TODO: 调用LLM进行分析
        # response = await reasoning_service.call_llm(prompt)

        # 临时模拟分析结果
        preference_analysis = {
            "attraction_preferences": {
                "preferred_types": ["历史文化", "现代建筑"],
                "must_visit": ["故宫", "天安门"],
                "avoid_types": ["极限运动"],
                "accessibility_needs": ["无障碍通道"],
                "time_preferences": {
                    "morning": "户外景点",
                    "afternoon": "室内景点",
                    "evening": "夜景观赏"
                },
                "activity_intensity": "适中",
                "crowd_tolerance": "无所谓",
                "photo_priority": "很重要",
                "cultural_interest": "适度了解"
            },
            "food_preferences": {
                "cuisine_types": ["北京菜", "川菜"],
                "dietary_restrictions": ["不吃辣"],
                "spice_tolerance": "微辣",
                "budget_per_meal": {
                    "breakfast": "30-50元",
                    "lunch": "80-120元",
                    "dinner": "150-250元"
                },
                "dining_scenarios": ["家庭聚餐", "特色小吃"],
                "meal_timing": {
                    "breakfast": "酒店内用餐",
                    "lunch": "景点附近",
                    "dinner": "市区特色餐厅"
                },
                "local_specialties": "特别期待",
                "dining_atmosphere": "无所谓"
            },
            "accommodation_preferences": {
                "hotel_level": "四星级",
                "location_priority": ["交通便利", "景点附近"],
                "room_requirements": ["双床房", "无烟房"],
                "amenities_needed": ["WiFi", "早餐", "停车场"],
                "amenities_preferred": ["健身房", "游泳池"],
                "budget_per_night": "400-600元",
                "booking_flexibility": "可调整",
                "check_in_preferences": {
                    "early_check_in": False,
                    "late_check_out": True
                },
                "special_services": [],
                "brand_preference": "连锁"
            },
            "preference_confidence": {
                "attraction_confidence": 0.85,
                "food_confidence": 0.8,
                "accommodation_confidence": 0.9,
                "overall_confidence": 0.85
            },
            "personalization_insights": [
                "用户偏好文化类景点，适合安排历史古迹游览",
                "对美食有一定要求，建议安排特色餐厅",
                "住宿要求较高，注重便利性和舒适度"
            ],
            "recommendation_strategy": {
                "primary_focus": "平衡",
                "decision_factors": ["文化价值", "便利性", "性价比"],
                "flexibility_areas": ["用餐时间", "景点顺序"]
            }
        }

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "preference_analysis",
                "success",
                preference_analysis
            )

        logger.info(f"[{task_id}] 个性化偏好分析完成")

        return {
            "preference_analysis": preference_analysis,
            "current_phase": "preference_analysis_completed"
        }

    except Exception as e:
        logger.error(f"[{task_id}] 个性化偏好分析失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "preference_analysis")

        return {
            "has_error": True,
            "error_message": f"个性化偏好分析失败: {str(e)}",
            "current_phase": "error"
        }


async def prepare_planning_context(state: StandardAgentState) -> Dict[str, Any]:
    """
    准备ICP规划上下文 (V3.0)

    整合两步分析结果，为ICP迭代规划准备统一的上下文
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始准备规划上下文")

    try:
        # 获取Planner Tools
        consolidate_tool = unified_registry.get_planner_tool("create_consolidated_intent")
        prepare_icp_tool = unified_registry.get_planner_tool("prepare_icp_context")
        extract_insights_tool = unified_registry.get_planner_tool("extract_key_insights")

        if not all([consolidate_tool, prepare_icp_tool, extract_insights_tool]):
            raise ValueError("Required planner tools not found")

        # 获取两步分析结果
        framework_analysis = state.get("framework_analysis", {})
        preference_analysis = state.get("preference_analysis", {})

        if not framework_analysis or not preference_analysis:
            raise ValueError("Framework or preference analysis results missing")

        # 整合意图
        consolidated_intent = consolidate_tool(framework_analysis, preference_analysis)

        # 提取关键洞察
        key_insights = extract_insights_tool(consolidated_intent)

        # 准备ICP上下文
        icp_context = prepare_icp_tool(consolidated_intent)

        # 发布规划上下文准备完成事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "context_preparation",
                "success",
                {
                    "consolidated_intent": consolidated_intent,
                    "key_insights": key_insights,
                    "icp_context": icp_context
                }
            )

        logger.info(f"[{task_id}] 规划上下文准备完成")

        return {
            "consolidated_intent": consolidated_intent,
            "key_insights": key_insights,
            "icp_context": icp_context,
            "current_phase": "planning_ready"
        }

    except Exception as e:
        logger.error(f"[{task_id}] 规划上下文准备失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "context_preparation")

        return {
            "has_error": True,
            "error_message": f"规划上下文准备失败: {str(e)}",
            "current_phase": "error"
        }


async def run_icp_planning(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行ICP迭代式上下文规划 (V3.0)

    实现"思考-行动-观察"的迭代循环，直到规划完成
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始ICP迭代规划")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "icp_planning",
            "ICP迭代规划",
            "开始智能迭代规划，为您制定详细行程..."
        )

    try:
        # 获取ICP工具
        think_tool = unified_registry.get_planner_tool("generate_planning_thought")
        action_tool = unified_registry.get_planner_tool("select_next_action")
        observe_tool = unified_registry.get_planner_tool("observe_action_result")
        update_tool = unified_registry.get_planner_tool("update_planning_state")
        check_tool = unified_registry.get_planner_tool("check_planning_completion")

        if not all([think_tool, action_tool, observe_tool, update_tool, check_tool]):
            raise ValueError("Required ICP tools not found")

        # 获取规划上下文
        icp_context = state.get("icp_context", {})
        if not icp_context:
            raise ValueError("ICP context not found")

        # 初始化规划状态
        current_planning_state = {
            "daily_plans": state.get("daily_plans", {}),
            "daily_time_tracker": state.get("daily_time_tracker", {}),
            "total_budget_tracker": state.get("total_budget_tracker", 0.0),
            "tool_results": state.get("tool_results", {}),
            "planning_log": state.get("planning_log", []),
            "consolidated_intent": state.get("consolidated_intent", {}),
            "accommodation_planned": False
        }

        # ICP迭代循环
        max_iterations = 10
        step_number = 1

        for iteration in range(max_iterations):
            logger.info(f"[{task_id}] ICP迭代 {iteration + 1}/{max_iterations}")

            # 步骤1: 思考
            thought_result = think_tool(
                current_planning_state,
                icp_context,
                step_number
            )

            # 发布思考日志
            if event_bus:
                await event_bus.notify_planning_log(
                    task_id,
                    thought_result.get("thought_content", ""),
                    step_number
                )

            # 步骤2: 选择行动
            available_tools = icp_context.get("available_tools", ["search_poi"])
            action_result = action_tool(
                thought_result,
                available_tools,
                current_planning_state
            )

            selected_action = action_result.get("selected_action", {})
            tool_name = selected_action.get("tool_name", "search_poi")
            tool_params = selected_action.get("parameters", {})

            # 步骤3: 执行行动
            try:
                if tool_name in ["search_poi", "geocode", "get_driving_route"]:
                    # 执行Action Tool
                    action_execution_result = await unified_registry.execute_action_tool(
                        tool_name,
                        task_id=task_id,
                        **tool_params
                    )
                else:
                    # 模拟其他工具执行
                    action_execution_result = {"status": "simulated", "data": []}

            except Exception as e:
                logger.warning(f"[{task_id}] 工具执行失败: {str(e)}")
                action_execution_result = None

            # 步骤4: 观察结果
            observation = observe_tool(
                selected_action,
                action_execution_result,
                current_planning_state
            )

            # 步骤5: 更新状态
            current_planning_state = update_tool(
                current_planning_state,
                selected_action,
                action_execution_result,
                observation
            )

            # 发布行程更新事件
            if observation.get("success", False) and "target_day" in selected_action:
                target_day = selected_action["target_day"]
                daily_plans = current_planning_state.get("daily_plans", {})
                if target_day in daily_plans:
                    for activity in daily_plans[target_day]:
                        if event_bus:
                            await event_bus.notify_itinerary_update(
                                task_id,
                                target_day,
                                activity
                            )

            # 步骤6: 检查完成情况
            completion_check = check_tool(current_planning_state, icp_context)

            step_number += 1

            # 如果规划完成，退出循环
            if completion_check.get("is_complete", False):
                logger.info(f"[{task_id}] ICP规划完成，共执行{iteration + 1}次迭代")
                break

            # 如果质量太低，也可以选择退出
            if completion_check.get("quality_score", 0) < 0.3 and iteration > 5:
                logger.warning(f"[{task_id}] 规划质量较低，提前结束迭代")
                break

        # 生成最终结果
        final_itinerary = {
            "daily_plans": current_planning_state.get("daily_plans", {}),
            "planning_summary": {
                "total_iterations": step_number - 1,
                "completion_rate": completion_check.get("completion_rate", 0),
                "quality_score": completion_check.get("quality_score", 0),
                "planning_log": current_planning_state.get("planning_log", [])
            },
            "metadata": {
                "planning_method": "ICP",
                "generated_at": datetime.now().isoformat(),
                "consolidated_intent": current_planning_state.get("consolidated_intent", {})
            }
        }

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "icp_planning",
                "success",
                final_itinerary
            )

        logger.info(f"[{task_id}] ICP迭代规划完成")

        return {
            "daily_plans": current_planning_state.get("daily_plans", {}),
            "daily_time_tracker": current_planning_state.get("daily_time_tracker", {}),
            "total_budget_tracker": current_planning_state.get("total_budget_tracker", 0.0),
            "tool_results": current_planning_state.get("tool_results", {}),
            "planning_log": current_planning_state.get("planning_log", []),
            "final_itinerary": final_itinerary,
            "is_completed": True,
            "current_phase": "completed"
        }

    except Exception as e:
        logger.error(f"[{task_id}] ICP迭代规划失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "icp_planning")

        return {
            "has_error": True,
            "error_message": f"ICP迭代规划失败: {str(e)}",
            "current_phase": "error"
        }
